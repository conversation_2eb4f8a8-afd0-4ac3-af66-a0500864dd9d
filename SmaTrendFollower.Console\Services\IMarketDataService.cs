using Alpaca.Markets;

namespace SmaTrendFollower.Services;

public interface IMarketDataService
{
    // === Historical Data ===

    /// <summary>
    /// Gets historical daily bars for stock/ETF symbols from Alpaca
    /// </summary>
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical minute bars for stock/ETF symbols from Alpaca
    /// </summary>
    Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical bars for multiple stock/ETF symbols from Alpaca
    /// </summary>
    Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets historical minute bars for multiple stock/ETF symbols from Alpaca with fallback to Polygon
    /// </summary>
    Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets index-level data (like SPX, VIX) from Polygon
    /// </summary>
    Task<decimal?> GetIndexValueAsync(string indexSymbol);

    /// <summary>
    /// Gets historical index data from Polygon
    /// </summary>
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);

    // === Account & Positions ===

    /// <summary>
    /// Gets current account information from Alpaca
    /// </summary>
    Task<IAccount> GetAccountAsync();

    /// <summary>
    /// Gets current positions from Alpaca
    /// </summary>
    Task<IReadOnlyList<IPosition>> GetPositionsAsync();

    /// <summary>
    /// Gets recent fills/executions from Alpaca
    /// </summary>
    Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100);

    // === Options Data (Polygon) ===

    /// <summary>
    /// Gets options chain data including Greeks, IV, and OI from Polygon
    /// </summary>
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null);

    /// <summary>
    /// Gets options chain data with optional strike price filtering for performance optimization
    /// </summary>
    /// <param name="underlyingSymbol">The underlying symbol</param>
    /// <param name="expirationDate">Optional expiration date filter</param>
    /// <param name="currentPrice">Current underlying price for strike band calculation</param>
    /// <param name="strikeBandPercent">Strike band percentage (e.g., 15 for ±15%)</param>
    /// <returns>Filtered options data</returns>
    Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null, decimal? currentPrice = null, double? strikeBandPercent = null);

    /// <summary>
    /// Gets VIX term structure data from Polygon
    /// </summary>
    Task<IEnumerable<VixTermData>> GetVixTermStructureAsync();

    // === Enhanced Options Data ===

    /// <summary>
    /// Gets options quotes with Greeks, IV, and OI for specific contracts
    /// </summary>
    Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols);

    /// <summary>
    /// Gets options chain with Greeks for protective put strategies
    /// </summary>
    Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30);

    /// <summary>
    /// Gets options chain for covered call strategies
    /// </summary>
    Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7);

    /// <summary>
    /// Gets universe of symbols with ADV filtering
    /// </summary>
    Task<IEnumerable<string>> GetUniverseWithAdvFilterAsync(decimal minAdv = 20_000_000m);

    // === VIX Analysis ===

    /// <summary>
    /// Gets current VIX value and 30-day SMA for volatility regime detection
    /// </summary>
    Task<VixAnalysis> GetVixAnalysisAsync();

    /// <summary>
    /// Gets VIX spike detection for position size throttling
    /// </summary>
    Task<bool> IsVixSpikeAsync(decimal threshold = 25.0m);
}

/// <summary>
/// Represents a bar of index data from Polygon
/// </summary>
public readonly record struct IndexBar(
    DateTime TimeUtc,
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume
);

/// <summary>
/// Represents options data from Polygon
/// </summary>
public readonly record struct OptionData(
    string Symbol,
    string UnderlyingSymbol,
    DateTime ExpirationDate,
    decimal Strike,
    string OptionType, // "call" or "put"
    decimal? LastPrice,
    decimal? Bid,
    decimal? Ask,
    long? Volume,
    long? OpenInterest,
    decimal? ImpliedVolatility,
    decimal? Delta,
    decimal? Gamma,
    decimal? Theta,
    decimal? Vega
);

/// <summary>
/// Represents VIX term structure data
/// </summary>
public readonly record struct VixTermData(
    DateTime ExpirationDate,
    decimal Price,
    int DaysToExpiration
);

/// <summary>
/// Represents options quote data with real-time pricing
/// </summary>
public readonly record struct OptionQuote(
    string Symbol,
    string UnderlyingSymbol,
    DateTime ExpirationDate,
    decimal Strike,
    string OptionType,
    decimal Bid,
    decimal Ask,
    decimal LastPrice,
    long Volume,
    long OpenInterest,
    decimal ImpliedVolatility,
    decimal Delta,
    decimal Gamma,
    decimal Theta,
    decimal Vega,
    DateTime Timestamp
);

/// <summary>
/// Represents VIX analysis for volatility regime detection
/// </summary>
public readonly record struct VixAnalysis(
    decimal CurrentVix,
    decimal VixSma30,
    decimal VixChange,
    decimal VixChangePercent,
    bool IsAboveSma,
    bool IsSpike,
    DateTime Timestamp
);

/// <summary>
/// Represents enhanced trading signal with volatility context
/// </summary>
public readonly record struct EnhancedTradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    decimal VixAdjustment,
    bool IsVixSpike,
    decimal VolatilityRank
);

using Prometheus;

namespace SmaTrendFollower.Monitoring;

/// <summary>
/// Central registry for all Prometheus metrics used in the SmaTrendFollower system.
/// Provides standardized metrics for trading operations, system performance, and observability.
/// </summary>
public static class MetricsRegistry
{
    // === Trading Metrics ===

    /// <summary>
    /// Counter for total trades executed, labeled by side (Buy/Sell)
    /// </summary>
    public static readonly Counter TradesTotal =
        Metrics.CreateCounter("trades_total", "Total number of trades executed",
            new CounterConfiguration { LabelNames = new[] { "side", "symbol" } });

    // === Enhanced System Resource Metrics ===

    /// <summary>
    /// Current CPU usage percentage
    /// </summary>
    public static readonly Gauge CpuUsagePercent =
        Metrics.CreateGauge("system_cpu_usage_percent", "Current CPU usage percentage");

    /// <summary>
    /// Current memory usage in bytes
    /// </summary>
    public static readonly Gauge MemoryUsageBytes =
        Metrics.CreateGauge("system_memory_usage_bytes", "Current memory usage in bytes");

    /// <summary>
    /// Available memory in bytes
    /// </summary>
    public static readonly Gauge MemoryAvailableBytes =
        Metrics.CreateGauge("system_memory_available_bytes", "Available memory in bytes");

    /// <summary>
    /// GC heap size in bytes
    /// </summary>
    public static readonly Gauge GcHeapSizeBytes =
        Metrics.CreateGauge("system_gc_heap_size_bytes", "GC heap size in bytes");

    /// <summary>
    /// Number of active threads
    /// </summary>
    public static readonly Gauge ThreadCount =
        Metrics.CreateGauge("system_thread_count", "Number of active threads");

    /// <summary>
    /// Disk I/O operations per second
    /// </summary>
    public static readonly Gauge DiskIopsTotal =
        Metrics.CreateGauge("system_disk_iops_total", "Disk I/O operations per second",
            new GaugeConfiguration { LabelNames = new[] { "operation" } });

    /// <summary>
    /// Network bytes transferred
    /// </summary>
    public static readonly Counter NetworkBytesTotal =
        Metrics.CreateCounter("system_network_bytes_total", "Network bytes transferred",
            new CounterConfiguration { LabelNames = new[] { "direction" } });

    /// <summary>
    /// System load average (1, 5, 15 minutes)
    /// </summary>
    public static readonly Gauge SystemLoadAverage =
        Metrics.CreateGauge("system_load_average", "System load average",
            new GaugeConfiguration { LabelNames = new[] { "period" } });

    /// <summary>
    /// Process uptime in seconds
    /// </summary>
    public static readonly Gauge ProcessUptimeSeconds =
        Metrics.CreateGauge("system_process_uptime_seconds", "Process uptime in seconds");

    /// <summary>
    /// File descriptor usage
    /// </summary>
    public static readonly Gauge FileDescriptorCount =
        Metrics.CreateGauge("system_file_descriptor_count", "Number of open file descriptors");

    /// <summary>
    /// System resource alerts triggered
    /// </summary>
    public static readonly Counter SystemResourceAlerts =
        Metrics.CreateCounter("system_resource_alerts_total", "System resource alerts triggered",
            new CounterConfiguration { LabelNames = new[] { "resource_type", "severity" } });

    /// <summary>
    /// Counter for trading signals generated, labeled by signal type and execution status
    /// </summary>
    public static readonly Counter SignalsTotal =
        Metrics.CreateCounter("signals_total", "Total number of trading signals generated",
            new CounterConfiguration { LabelNames = new[] { "signal_type", "executed" } });

    /// <summary>
    /// Histogram for signal generation latency in milliseconds
    /// </summary>
    public static readonly Histogram SignalLatencyMs =
        Metrics.CreateHistogram("signal_latency_ms", "Signal generation latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.LinearBuckets(1, 5, 20) });

    /// <summary>
    /// Gauge for current portfolio value in USD
    /// </summary>
    public static readonly Gauge PortfolioValueUsd =
        Metrics.CreateGauge("portfolio_value_usd", "Current portfolio value in USD");

    /// <summary>
    /// Gauge for current cash balance in USD
    /// </summary>
    public static readonly Gauge CashBalanceUsd =
        Metrics.CreateGauge("cash_balance_usd", "Current cash balance in USD");

    /// <summary>
    /// Gauge for daily P&L in USD
    /// </summary>
    public static readonly Gauge DailyPnlUsd =
        Metrics.CreateGauge("daily_pnl_usd", "Daily profit and loss in USD");

    // === Market Data Metrics ===
    
    /// <summary>
    /// Counter for WebSocket reconnections, labeled by channel
    /// </summary>
    public static readonly Counter WsReconnects =
        Metrics.CreateCounter("websocket_reconnect_total", "Total WebSocket reconnections",
            new CounterConfiguration { LabelNames = new[] { "channel", "reason" } });

    /// <summary>
    /// Gauge for current universe size (number of symbols being monitored)
    /// </summary>
    public static readonly Gauge CurrentUniverseSize =
        Metrics.CreateGauge("universe_size", "Current number of symbols in trading universe");

    /// <summary>
    /// Counter for market data requests, labeled by source and status
    /// </summary>
    public static readonly Counter MarketDataRequests =
        Metrics.CreateCounter("market_data_requests_total", "Total market data requests",
            new CounterConfiguration { LabelNames = new[] { "source", "status" } });

    /// <summary>
    /// Histogram for market data fetch latency in milliseconds
    /// </summary>
    public static readonly Histogram MarketDataLatencyMs =
        Metrics.CreateHistogram("market_data_latency_ms", "Market data fetch latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.ExponentialBuckets(1, 2, 15) });

    /// <summary>
    /// Gauge for data staleness in minutes (age of most recent data)
    /// </summary>
    public static readonly Gauge DataStalenessMinutes =
        Metrics.CreateGauge("data_staleness_minutes", "Age of most recent market data in minutes",
            new GaugeConfiguration { LabelNames = new[] { "data_type", "symbol" } });

    // === Enhanced WebSocket Metrics ===

    /// <summary>
    /// Gauge for WebSocket connection status
    /// </summary>
    public static readonly Gauge WebSocketConnectionStatus =
        Metrics.CreateGauge("websocket_connection_status", "WebSocket connection status (1=connected, 0=disconnected)",
            new GaugeConfiguration { LabelNames = new[] { "service", "channel" } });

    /// <summary>
    /// Counter for WebSocket messages received
    /// </summary>
    public static readonly Counter WebSocketMessagesReceived =
        Metrics.CreateCounter("websocket_messages_received_total", "Total WebSocket messages received",
            new CounterConfiguration { LabelNames = new[] { "service", "message_type" } });

    /// <summary>
    /// Counter for WebSocket messages sent
    /// </summary>
    public static readonly Counter WebSocketMessagesSent =
        Metrics.CreateCounter("websocket_messages_sent_total", "Total WebSocket messages sent",
            new CounterConfiguration { LabelNames = new[] { "service", "message_type" } });

    /// <summary>
    /// Histogram for WebSocket message processing latency
    /// </summary>
    public static readonly Histogram WebSocketMessageLatencyMs =
        Metrics.CreateHistogram("websocket_message_latency_ms", "WebSocket message processing latency in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "service", "message_type" }, Buckets = Histogram.ExponentialBuckets(1, 2, 15) });

    /// <summary>
    /// Gauge for WebSocket subscription count
    /// </summary>
    public static readonly Gauge WebSocketSubscriptionCount =
        Metrics.CreateGauge("websocket_subscription_count", "Number of active WebSocket subscriptions",
            new GaugeConfiguration { LabelNames = new[] { "service", "channel" } });

    /// <summary>
    /// Counter for WebSocket errors
    /// </summary>
    public static readonly Counter WebSocketErrors =
        Metrics.CreateCounter("websocket_errors_total", "Total WebSocket errors",
            new CounterConfiguration { LabelNames = new[] { "service", "error_type" } });

    // === API Performance Metrics ===

    /// <summary>
    /// Histogram for API request duration
    /// </summary>
    public static readonly Histogram ApiRequestDurationMs =
        Metrics.CreateHistogram("api_request_duration_ms", "API request duration in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "service", "endpoint", "method" }, Buckets = Histogram.ExponentialBuckets(10, 2, 15) });

    /// <summary>
    /// Counter for API requests by status code
    /// </summary>
    public static readonly Counter ApiRequestsTotal =
        Metrics.CreateCounter("api_requests_total", "Total API requests",
            new CounterConfiguration { LabelNames = new[] { "service", "endpoint", "method", "status_code" } });

    /// <summary>
    /// Gauge for API rate limit remaining
    /// </summary>
    public static readonly Gauge ApiRateLimitRemaining =
        Metrics.CreateGauge("api_rate_limit_remaining", "API rate limit remaining",
            new GaugeConfiguration { LabelNames = new[] { "service" } });

    /// <summary>
    /// Counter for API rate limit exceeded events
    /// </summary>
    public static readonly Counter ApiRateLimitExceeded =
        Metrics.CreateCounter("api_rate_limit_exceeded_total", "API rate limit exceeded events",
            new CounterConfiguration { LabelNames = new[] { "service" } });

    // === System Performance Metrics ===
    
    /// <summary>
    /// Counter for API rate limit hits, labeled by service
    /// </summary>
    public static readonly Counter RateLimitHits =
        Metrics.CreateCounter("rate_limit_hits_total", "Total API rate limit hits",
            new CounterConfiguration { LabelNames = new[] { "service", "endpoint" } });

    /// <summary>
    /// Counter for circuit breaker trips, labeled by service
    /// </summary>
    public static readonly Counter CircuitBreakerTrips =
        Metrics.CreateCounter("circuit_breaker_trips_total", "Total circuit breaker trips",
            new CounterConfiguration { LabelNames = new[] { "service", "reason" } });

    /// <summary>
    /// Histogram for Redis operation latency in milliseconds
    /// </summary>
    public static readonly Histogram RedisLatencyMs =
        Metrics.CreateHistogram("redis_latency_ms", "Redis operation latency in milliseconds",
            new HistogramConfiguration { Buckets = Histogram.LinearBuckets(0.1, 0.5, 20) });

    /// <summary>
    /// Counter for Redis cache hits and misses
    /// </summary>
    public static readonly Counter RedisCacheOperations =
        Metrics.CreateCounter("redis_cache_operations_total", "Total Redis cache operations",
            new CounterConfiguration { LabelNames = new[] { "operation", "result" } });

    /// <summary>
    /// Gauge for current Redis connection count
    /// </summary>
    public static readonly Gauge RedisConnections =
        Metrics.CreateGauge("redis_connections", "Current number of Redis connections");

    // === Database Performance Metrics ===

    /// <summary>
    /// Histogram for database query execution time
    /// </summary>
    public static readonly Histogram DatabaseQueryDurationMs =
        Metrics.CreateHistogram("database_query_duration_ms", "Database query execution time in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "database", "operation" }, Buckets = Histogram.ExponentialBuckets(1, 2, 15) });

    /// <summary>
    /// Counter for database operations
    /// </summary>
    public static readonly Counter DatabaseOperationsTotal =
        Metrics.CreateCounter("database_operations_total", "Total database operations",
            new CounterConfiguration { LabelNames = new[] { "database", "operation", "status" } });

    /// <summary>
    /// Gauge for database connection pool usage
    /// </summary>
    public static readonly Gauge DatabaseConnectionPoolUsage =
        Metrics.CreateGauge("database_connection_pool_usage", "Database connection pool usage",
            new GaugeConfiguration { LabelNames = new[] { "database", "pool_type" } });

    /// <summary>
    /// Counter for database connection pool exhaustion events
    /// </summary>
    public static readonly Counter DatabaseConnectionPoolExhaustion =
        Metrics.CreateCounter("database_connection_pool_exhaustion_total", "Database connection pool exhaustion events",
            new CounterConfiguration { LabelNames = new[] { "database" } });

    /// <summary>
    /// Histogram for Redis operation latency
    /// </summary>
    public static readonly Histogram RedisOperationDurationMs =
        Metrics.CreateHistogram("redis_operation_duration_ms", "Redis operation duration in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "operation", "database" }, Buckets = Histogram.ExponentialBuckets(0.1, 2, 15) });

    // Removed duplicate RedisCacheOperations definition (already defined above)

    /// <summary>
    /// Gauge for Redis memory usage
    /// </summary>
    public static readonly Gauge RedisMemoryUsageBytes =
        Metrics.CreateGauge("redis_memory_usage_bytes", "Redis memory usage in bytes");

    /// <summary>
    /// Gauge for Redis key count by pattern
    /// </summary>
    public static readonly Gauge RedisKeyCount =
        Metrics.CreateGauge("redis_key_count", "Redis key count by pattern",
            new GaugeConfiguration { LabelNames = new[] { "pattern" } });

    /// <summary>
    /// Counter for Redis connection failures
    /// </summary>
    public static readonly Counter RedisConnectionFailures =
        Metrics.CreateCounter("redis_connection_failures_total", "Redis connection failures");

    // === Machine Learning Metrics ===

    /// <summary>
    /// Counter for ML model retraining runs
    /// </summary>
    public static readonly Counter MLRetrainRuns =
        Metrics.CreateCounter("ml_retrain_total", "Total ML model retraining runs",
            new CounterConfiguration { LabelNames = new[] { "result" } });

    /// <summary>
    /// Gauge for ML model accuracy from latest training
    /// </summary>
    public static readonly Gauge MLModelAccuracy =
        Metrics.CreateGauge("ml_model_accuracy", "Current ML model accuracy");

    /// <summary>
    /// Gauge for ML model version (timestamp)
    /// </summary>
    public static readonly Gauge MLModelVersion =
        Metrics.CreateGauge("ml_model_version", "Current ML model version timestamp");

    /// <summary>
    /// Histogram for ML model retraining duration
    /// </summary>
    public static readonly Histogram MLRetrainDuration =
        Metrics.CreateHistogram("ml_retrain_duration_seconds", "ML model retraining duration in seconds");

    /// <summary>
    /// Counter for ML signal predictions
    /// </summary>
    public static readonly Counter MLPredictions =
        Metrics.CreateCounter("ml_predictions_total", "Total ML signal predictions",
            new CounterConfiguration { LabelNames = new[] { "threshold_passed" } });

    /// <summary>
    /// Histogram for ML prediction scores
    /// </summary>
    public static readonly Histogram MLPredictionScores =
        Metrics.CreateHistogram("ml_prediction_scores", "Distribution of ML prediction scores",
            new HistogramConfiguration { Buckets = new[] { 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0 } });

    /// <summary>
    /// Gauge for position size as percentage of equity
    /// </summary>
    public static readonly Gauge PositionSizePct =
        Metrics.CreateGauge("position_size_pct", "Position size as percentage of equity",
            new GaugeConfiguration { LabelNames = new[] { "method" } });

    // === Slippage Forecasting Metrics ===

    /// <summary>
    /// Histogram for actual slippage in basis points
    /// </summary>
    public static readonly Histogram SlippageActualBps =
        Metrics.CreateHistogram("slippage_actual_bps", "Actual slippage in basis points",
            new HistogramConfiguration { Buckets = new[] { -50.0, -25.0, -10.0, -5.0, -2.0, -1.0, 0.0, 1.0, 2.0, 5.0, 10.0, 25.0, 50.0 } });

    /// <summary>
    /// Histogram for predicted slippage in basis points
    /// </summary>
    public static readonly Histogram SlippagePredictedBps =
        Metrics.CreateHistogram("slippage_predicted_bps", "Predicted slippage in basis points",
            new HistogramConfiguration { Buckets = new[] { -50.0, -25.0, -10.0, -5.0, -2.0, -1.0, 0.0, 1.0, 2.0, 5.0, 10.0, 25.0, 50.0 } });

    /// <summary>
    /// Counter for total slippage savings in basis points (predicted - actual, positive = saved)
    /// </summary>
    public static readonly Counter SlippageSavedTotalBps =
        Metrics.CreateCounter("slippage_saved_total_bps", "Total slippage savings in basis points");

    /// <summary>
    /// Histogram for slippage prediction accuracy (absolute error in basis points)
    /// </summary>
    public static readonly Histogram SlippagePredictionErrorBps =
        Metrics.CreateHistogram("slippage_prediction_error_bps", "Slippage prediction error in basis points",
            new HistogramConfiguration { Buckets = new[] { 0.0, 1.0, 2.0, 5.0, 10.0, 15.0, 20.0, 30.0, 50.0 } });

    /// <summary>
    /// Counter for slippage forecaster model retraining runs
    /// </summary>
    public static readonly Counter SlippageRetrainTotal =
        Metrics.CreateCounter("slippage_retrain_total", "Total slippage model retraining runs",
            new CounterConfiguration { LabelNames = new[] { "model_type", "result" } });

    /// <summary>
    /// Gauge for slippage model accuracy (R-squared)
    /// </summary>
    public static readonly Gauge SlippageModelAccuracy =
        Metrics.CreateGauge("slippage_model_accuracy", "Current slippage model R-squared accuracy");

    /// <summary>
    /// Gauge for slippage model version (timestamp)
    /// </summary>
    public static readonly Gauge SlippageModelVersion =
        Metrics.CreateGauge("slippage_model_version", "Current slippage model version timestamp");

    // === Risk Management Metrics ===
    
    /// <summary>
    /// Counter for risk checks performed, labeled by check type and result
    /// </summary>
    public static readonly Counter RiskChecks =
        Metrics.CreateCounter("risk_checks_total", "Total risk checks performed",
            new CounterConfiguration { LabelNames = new[] { "check_type", "result" } });

    /// <summary>
    /// Counter for trades blocked by risk management
    /// </summary>
    public static readonly Counter TradesBlocked =
        Metrics.CreateCounter("trades_blocked_total", "Total trades blocked by risk management",
            new CounterConfiguration { LabelNames = new[] { "reason" } });

    /// <summary>
    /// Gauge for current position count
    /// </summary>
    public static readonly Gauge CurrentPositions =
        Metrics.CreateGauge("current_positions", "Current number of open positions");

    /// <summary>
    /// Gauge for current exposure as percentage of portfolio
    /// </summary>
    public static readonly Gauge CurrentExposurePercent =
        Metrics.CreateGauge("current_exposure_percent", "Current market exposure as percentage of portfolio");

    // === Error Tracking Metrics ===
    
    /// <summary>
    /// Counter for application errors, labeled by component and error type
    /// </summary>
    public static readonly Counter ApplicationErrors =
        Metrics.CreateCounter("application_errors_total", "Total application errors",
            new CounterConfiguration { LabelNames = new[] { "component", "error_type" } });

    /// <summary>
    /// Counter for order execution failures, labeled by reason
    /// </summary>
    public static readonly Counter OrderFailures =
        Metrics.CreateCounter("order_failures_total", "Total order execution failures",
            new CounterConfiguration { LabelNames = new[] { "reason", "symbol" } });

    // === VIX and Market Regime Metrics ===
    
    /// <summary>
    /// Gauge for current VIX value
    /// </summary>
    public static readonly Gauge CurrentVix =
        Metrics.CreateGauge("current_vix", "Current VIX value");

    /// <summary>
    /// Counter for VIX data source fallbacks
    /// </summary>
    public static readonly Counter VixFallbacks =
        Metrics.CreateCounter("vix_fallbacks_total", "Total VIX data source fallbacks",
            new CounterConfiguration { LabelNames = new[] { "from_source", "to_source" } });

    /// <summary>
    /// Gauge for market regime indicator (0=Bear, 1=Bull, 0.5=Neutral)
    /// </summary>
    public static readonly Gauge MarketRegime =
        Metrics.CreateGauge("market_regime", "Current market regime indicator");

    /// <summary>
    /// Gauge for ML-based regime classification (0=Sideways, 1=TrendingUp, 2=TrendingDown, 3=Panic)
    /// </summary>
    public static readonly Gauge RegimeState =
        Metrics.CreateGauge("regime_state", "Current ML-based market regime classification");

    /// <summary>
    /// Counter for regime classification changes
    /// </summary>
    public static readonly Counter RegimeChanges =
        Metrics.CreateCounter("regime_changes_total", "Total regime classification changes",
            new CounterConfiguration { LabelNames = new[] { "from", "to" } });

    // === Background Service Metrics ===

    /// <summary>
    /// Counter for background service executions
    /// </summary>
    public static readonly Counter BackgroundServiceExecutions =
        Metrics.CreateCounter("background_service_executions_total", "Total background service executions",
            new CounterConfiguration { LabelNames = new[] { "service_name", "status" } });

    /// <summary>
    /// Histogram for background service execution duration
    /// </summary>
    public static readonly Histogram BackgroundServiceDurationMs =
        Metrics.CreateHistogram("background_service_duration_ms", "Background service execution duration in milliseconds",
            new HistogramConfiguration { LabelNames = new[] { "service_name" }, Buckets = Histogram.ExponentialBuckets(100, 2, 15) });

    // === Discord Notification Metrics ===

    /// <summary>
    /// Counter for Discord messages sent by Serilog sink, labeled by log level
    /// </summary>
    public static readonly Counter DiscordMessagesTotal =
        Metrics.CreateCounter("discord_messages_total", "Total number of Discord messages sent by Serilog sink",
            new CounterConfiguration { LabelNames = new[] { "level" } });

    /// <summary>
    /// Counter for Discord sink errors
    /// </summary>
    public static readonly Counter DiscordSinkErrorsTotal =
        Metrics.CreateCounter("discord_sink_errors_total", "Total number of Discord sink errors");

    // === HTTP Client Latency Metrics (per directive) ===

    /// <summary>
    /// HTTP client latency histogram with client labels (alpaca, polygon, finbert)
    /// Buckets: 1ms to 512ms exponential scale for typical API response times
    /// </summary>
    public static readonly Histogram HttpLatency = Metrics.CreateHistogram(
        "http_client_latency_ms",
        "Latency of outbound HTTP calls in milliseconds",
        new HistogramConfiguration
        {
            LabelNames = new[] { "client" },
            Buckets = Histogram.ExponentialBuckets(1, 2, 10)  // 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 ms
        });

    // === Redis Operations Metrics (per directive) ===

    /// <summary>
    /// Redis operations counter with operation type labels (read, write)
    /// Tracks total number of Redis operations for performance monitoring
    /// </summary>
    public static readonly Counter RedisOps = Metrics.CreateCounter(
        "redis_ops_total",
        "Total number of Redis operations",
        new CounterConfiguration
        {
            LabelNames = new[] { "type" }
        });

    // === Universe Size Metric (per directive) ===

    /// <summary>
    /// Current universe size gauge
    /// Tracks the number of tradable symbols in today's universe after filtering
    /// </summary>
    public static readonly Gauge UniverseSize =
        Metrics.CreateGauge("universe_size", "Number of tradable symbols in today's universe");

    // === Trading Cycle Duration Metric (per directive) ===

    /// <summary>
    /// Trading cycle duration histogram
    /// Measures wall-clock time for complete trading cycles from start to finish
    /// Buckets: 50ms to 51.2s exponential scale for trading cycle timing
    /// </summary>
    public static readonly Histogram CycleDuration = Metrics.CreateHistogram(
        "cycle_duration_ms",
        "Trading cycle wall time in milliseconds",
        new HistogramConfiguration
        {
            Buckets = Histogram.ExponentialBuckets(50, 2, 10) // 50, 100, 200, 400, 800, 1600, 3200, 6400, 12800, 25600 ms
        });
}

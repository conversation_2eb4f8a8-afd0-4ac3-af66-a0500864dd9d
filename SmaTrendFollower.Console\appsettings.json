{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "UniverseCacheConnection": "localhost:6379"
  },
  "Universe": {
    "UsePolygon": true,
    "MarketCapMin": 5000000000,
    "AvgVolumeMin": 1000000,
    "MaxSymbols": 200
  },
  "UniverseCache": {
    "RefreshInterval": "00:10:00"
  },

  // Enable Polygon-based universe management
  "UsePolygonUniverse": true,

  // Polygon Symbol Universe Configuration
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },

  // Polygon Snapshot Service Configuration
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },

  // Daily Universe Refresh Configuration
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 10.0,
    "MinAverageVolume": 1000000,
    "MinVolatilityPercent": 2.0,
    "MaxCandidates": 200,
    "AnalysisPeriodDays": 20,
    "MinMarketCap": null,
    "CacheTtlHours": 24,
    "ExcludedExchanges": [],
    "ExcludedTypes": []
  },

  // Enhanced DynamicUniverseProvider Configuration
  "DynamicUniverse": {
    "UsePolygonIntegration": true,
    "FallbackToStaticSymbols": true,
    "MaxConcurrentBatches": 3,
    "BatchSize": 20,
    "DefaultCriteria": {
      "MinPrice": 10.0,
      "MinAverageVolume": 1000000,
      "MinVolatilityPercent": 2.0,
      "AnalysisPeriodDays": 20,
      "MaxSymbols": 200
    }
  },

  "ML": {
    "PositionModelPath": "Model/position_model.zip"
  },
  "SlippageTraining": {
    "ModelOutputPath": "Model/slippage_model.zip"
  },
  "VWAP": {
    "StartHour": 9,
    "StartMinute": 40
  },
  "AllowedHosts": "*"
}
